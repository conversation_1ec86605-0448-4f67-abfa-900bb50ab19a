import 'package:flutter/material.dart';

class FooterComponent extends StatelessWidget {
  final double width;
  final double height;

  const FooterComponent({
    Key? key,
    this.width = 1272,
    this.height = 95,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 28),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildButton(
            text: 'CREATE TABLE',
            backgroundColor: const Color(0xFF1579B2),
            onPressed: () {
              // Handle create table action
              Navigator.pushNamed(context, '/casino');
            },
          ),
          const SizedBox(width: 16),
          _buildButton(
            text: 'JOIN RANDOM',
            backgroundColor: const Color(0xFF33A076),
            onPressed: () {
              // Handle join random action
              
            },
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required Color backgroundColor,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Container(
        height: 67,
        // decoration: BoxDecoration(
        //   color: backgroundColor,
        //   borderRadius: BorderRadius.circular(16),
        //   border: Border.all(
        //     color: const Color(0xCC000000),
        //     width: 2.5,
        //   ),
        // ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(16),
            child: Center(
              child: text == 'CREATE TABLE' ? Image.asset('assets/images/CreateGame.png') : Image.asset('assets/images/JoinGame.png'),
            ),
          ),
        ),
      ),
    );
  }
}

