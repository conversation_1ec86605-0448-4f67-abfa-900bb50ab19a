import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

class MyMenuBar extends StatelessWidget {
  final List<String> navLinks;
  final VoidCallback onRegister;
  final VoidCallback onLogin;

  const MyMenuBar({
    Key? key,
    this.navLinks = const [
      'CASINO',
      'TOURNAMENTS',
      'PROMOTIONS',
      'GAMES',
      'COMMUNITY',
      'SUPPORT',
    ],
    required this.onRegister,
    required this.onLogin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      // width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 40),
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Color(0xFF8BEFDB)],
              stops: [0.1923, 1.0],
            ).createShader(bounds),
            child: Image.asset(
              'assets/images/4LeafPoker.png',
              width: 100,
            ),
          ),

          // Navigation Links
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: navLinks
                .map((link) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: _NavLink(text: link),
                    ))
                .toList(),
          ),

          // Action Buttons
          Row(
            children: [
              _ActionButton(
                text: 'REGISTER',
                gradientColors: [Color(0xFF33A076), Color(0xFF28A065)],
                onPressed: onRegister,
              ),
              const SizedBox(width: 14.4),
              _ActionButton(
                text: 'LOG IN',
                gradientColors: [Color(0xFF1579B2), Color(0xFF126DA1)],
                onPressed: onLogin,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _NavLink extends StatelessWidget {
  final String text;

  const _NavLink({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
        cursor: SystemMouseCursors.click,
        child: TextButton(
          onPressed: () {
            Navigator.pushNamed(
              context,
              '/${text.toLowerCase()}',
            );
          },
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15.51,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
            ),
          ),
        ));
  }
}

class _ActionButton extends StatefulWidget {
  final String text;
  final List<Color> gradientColors;
  final VoidCallback onPressed;

  const _ActionButton({
    Key? key,
    required this.text,
    required this.gradientColors,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_ActionButton> createState() => _ActionButtonState();
}

class _ActionButtonState extends State<_ActionButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 13,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: widget.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(13.3),
            // border: Border.all(
            //   color: Colors.black.withOpacity(0.8),
            //   width: 2.77,
            // ),
          ),
          child: Text(
            widget.text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 17.73,
              fontWeight: FontWeight.w900,
              fontFamily: 'Inter',
            ),
          ),
        ),
      ),
    );
  }
}
