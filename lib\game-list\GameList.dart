import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:four_leaf_poker/api.dart';
import 'package:four_leaf_poker/game-list/GameListComponent.dart';
import 'package:four_leaf_poker/game-list/game_lobby_layout.dart';

import 'package:universal_html/html.dart' as html;

import '../table/header/custom_menu.dart';


/// Offsets to apply on mobile: first = circle offset, second = tab offset (unused here)
class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

class GameListPage extends StatefulWidget {
  const GameListPage({Key? key}) : super(key: key);

  @override
  State<GameListPage> createState() => _GameListPageState();
}

class _GameListPageState extends State<GameListPage> {
  // The seat-count

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;
    final games = [
      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20',
        'buyIn': '\$2,500',
        'players': '8',
        'wait': '0:00',
        'hours': '4.3',
      },
      // Add more games as needed
      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20',
        'buyIn': '\$2,500',
        'players': '8',
        'wait': '0:00',
        'hours': '4.3',
      },  
      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20',
        'buyIn': '\$2,500',
        'players': '8',
        'wait': '0:00',
        'hours': '4.3',
      },
    ];
    return Scaffold(
      backgroundColor: const Color(0xFF383838),
      body: SizedBox.expand(
    child:  Stack(
        children: [

          // Table graphic + seats
          Positioned.fill(
            child: Container(

              decoration: const BoxDecoration(
                color: Color.fromARGB(255, 7, 32, 30),
                image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                    opacity: .2),
              ),
            ),
          ),
          //   child: WinningsDisplay())
         Align(
          alignment: Alignment.topCenter,
            child: CustomMenu(onMenuPressed: ()=>{}, onSettingsPressed: ()=>{}, showCountdown: false),
         ),
         Center(
          child: GameLobbyLayout()
         ),          
         Align(
          alignment: Alignment.bottomLeft,
            child: Image.asset(
              'assets/images/leprachaun2.png',
              //width: 200,
              height: MediaQuery.of(context).size.height * 0.67,
              fit: BoxFit.contain,
              opacity: const AlwaysStoppedAnimation(1),
            ),
         ) 
        ],
      ),)
    );
  }

 

}
