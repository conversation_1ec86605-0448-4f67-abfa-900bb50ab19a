import 'package:flutter/material.dart';
import '../../action_history.dart';
import 'header_component.dart';
import 'body_component.dart';
import 'footer_component.dart';

class ActionLogModal extends StatefulWidget {
  final HistoryPanelState panelState;
  final VoidCallback onPanelToggle;
  final List<String> handHistory;
  final ScrollController historyScrollController;
  final VoidCallback onCopyHistory;
  final VoidCallback? onSwapCardView;
  final VoidCallback? onExitTable;

  const ActionLogModal({
    Key? key,
    required this.panelState,
    required this.onPanelToggle,
    required this.handHistory,
    required this.historyScrollController,
    required this.onCopyHistory,
    this.onSwapCardView,
    this.onExitTable,
  }) : super(key: key);

  @override
  _ActionLogModalState createState() => _ActionLogModalState();
}

class _ActionLogModalState extends State<ActionLogModal> {
  bool action_log_opened = false;

  void toggleActionLog() {
    setState(() {
      print("Toggling action log...  $action_log_opened");
      action_log_opened = !action_log_opened;
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    return AnimatedContainer(
      width: 302,
      duration: Duration(milliseconds: 200),
      height: action_log_opened ? 700 : 100,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xCC000000), width: 2.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: InkWell(
              child: Icon(
                Icons.menu,
                color: Colors.black,
                size: 12,
              ),
            ),
          ),
          const SizedBox(height: 8),
          HeaderComponent(
            onMenuTap: toggleActionLog,
            actionLogOpened: action_log_opened,
          ),
          if (action_log_opened) ...[
            Expanded(
              child: Column(
                children: [
                  const SizedBox(height: 8),
                  Expanded(
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _buildContentForState(widget.panelState, isMobile),
                    ),
                  ),
                  // const SizedBox(height: 8),
                  // const FooterComponent(),
                  // const SizedBox(height: 8),
                ],
              ),
            ),
          ] else ...[
            const SizedBox(height: 8),
          ],
        ],
      ),
    );
  }

  Widget _buildContentForState(HistoryPanelState state, bool isMobile) {
    switch (state) {
      case HistoryPanelState.closed:
        return const SizedBox.shrink();
      case HistoryPanelState.semiOpen:
        return _buildSemiOpenHistory(isMobile);
      case HistoryPanelState.fullyOpen:
        return _buildFullyOpenHistory();
    }
  }

  Widget _buildSemiOpenHistory(bool isMobile) {
    final displayed = takeLast(widget.handHistory, isMobile ? 2 : 5);
    return Container(
      height: double.infinity,
      clipBehavior: Clip.none,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Color.fromARGB(255, 0, 39, 41),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: List.generate(displayed.length, (index) {
          final isLast = index == displayed.length - 1;
          return Container(
            decoration: BoxDecoration(
              borderRadius: isLast
                  ? const BorderRadius.only(
                      bottomLeft: Radius.circular(6),
                      bottomRight: Radius.circular(6),
                    )
                  : null,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            child: Row(
              children: [
                Flexible(
                  flex: 1,
                  child: Stack(children: [
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 4
                          ..color = Colors.black,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      displayed[index].startsWith('Player')
                          ? displayed[index].substring(0, 8)
                          : displayed[index],
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ]),
                ),
                SizedBox(
                  width: 2,
                ),
                if (displayed[index].startsWith('Player') &&
                    displayed[index].length > 8)
                  Flexible(
                    flex: 2,
                    child: Stack(
                      children: [
                        Text(
                          displayed[index].substring(8),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 4
                              ..color = Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          displayed[index].substring(8),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            color: Color.fromARGB(255, 112, 112, 112),
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFullyOpenHistory() {
    return Scrollbar(
      thumbVisibility: true,
      controller: widget.historyScrollController,
      child: ListView.builder(
        controller: widget.historyScrollController,
        itemCount: widget.handHistory.length,
        itemBuilder: (context, index) {
          final bgColor = (index % 2 == 0)
              ? Colors.blueGrey.shade800
              : Colors.blueGrey.shade600;
          return Container(
            color: bgColor,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            child: Text(
              widget.handHistory[index],
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          );
        },
      ),
    );
  }

  List<String> takeLast(List<String> list, int n) {
    if (n <= 0) return [];
    if (n >= list.length) return list;
    return list.sublist(list.length - n);
  }
}
